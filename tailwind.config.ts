import type { Config } from 'tailwindcss'

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter', 'system-ui', 'sans-serif'],
      },
      colors: {
        // Base color palettes
        primary: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        },
        teal: {
          50: '#f0fdfa',
          100: '#ccfbf1',
          200: '#99f6e4',
          300: '#5eead4',
          400: '#2dd4bf',
          500: '#14b8a6',
          600: '#0d9488',
          700: '#0f766e',
          800: '#115e59',
          900: '#134e4a',
          950: '#042f2e',
        },
        slate: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#0f172a',
          950: '#020617',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
          950: '#030712',
        },
        red: {
          50: '#fef2f2',
          100: '#fee2e2',
          200: '#fecaca',
          300: '#fca5a5',
          400: '#f87171',
          500: '#ef4444',
          600: '#dc2626',
          700: '#b91c1c',
          800: '#991b1b',
          900: '#7f1d1d',
          950: '#450a0a',
        },
        orange: {
          50: '#fff7ed',
          100: '#ffedd5',
          200: '#fed7aa',
          300: '#fdba74',
          400: '#fb923c',
          500: '#f97316',
          600: '#ea580c',
          700: '#c2410c',
          800: '#9a3412',
          900: '#7c2d12',
          950: '#431407',
        },
        // Brand-specific semantic colors
        brand: {
          primary: '#0d9488',           // teal-600
          'primary-light': '#14b8a6',   // teal-500
          'primary-dark': '#0f766e',    // teal-700
          secondary: '#1e293b',         // slate-800
          'secondary-light': '#334155', // slate-700
          accent: '#fb923c',            // orange-400
          'action-button': '#0E3A53',
          'page-background': '#F6F2EA',
          'request-demo-bg': '#160625',
          'social-button-bg': '#F3F9FA',
        },
        // Text colors
        text: {
          primary: '#111827',    // gray-900
          secondary: '#4b5563',  // gray-600
          muted: '#6b7280',      // gray-500
          white: '#ffffff',
        },
        // Background colors
        background: {
          white: '#ffffff',
          light: '#f9fafb',      // gray-50
          dark: '#0f172a',       // slate-900
          auth: '#F6F2EA',
        },
        // Border colors
        border: {
          light: '#e5e7eb',      // gray-200
          medium: '#d1d5db',     // gray-300
          dark: '#9ca3af',       // gray-400
        },
        // Status colors
        success: '#0d9488',      // teal-600
        warning: '#f97316',      // orange-500
        error: '#ef4444',        // red-500
      },
    },
  },
  plugins: [],
} satisfies Config