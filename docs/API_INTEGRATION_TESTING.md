# API Integration Testing Guide

This guide helps you test the frontend-backend integration and troubleshoot any CORS issues.

## Quick Start

1. **Start the backend services:**
   ```bash
   cd Biormika-AWS-Backend
   npm run offline
   ```
   This starts:
   - API Gateway on http://localhost:3001/dev
   - WebSocket on ws://localhost:3003

2. **Run the API integration tests:**
   ```bash
   cd biormika-new-frontend
   npm run test:api
   ```

3. **Start the frontend dev server:**
   ```bash
   cd biormika-new-frontend
   npm run dev
   ```

## Development Proxy Configuration

The Vite development server is configured to proxy API requests to the local backend:

```typescript
// vite.config.ts
proxy: {
  '/auth': 'http://localhost:3001',
  '/files': 'http://localhost:3001',
  '/analysis': 'http://localhost:3001',
  '/user': 'http://localhost:3001',
}
```

This means when your frontend makes a request to `/auth/login`, Vite automatically forwards it to `http://localhost:3001/auth/login`.

## CORS Configuration

### Current Setup
- **Development**: All origins allowed (`Access-Control-Allow-Origin: *`)
- **Credentials**: Enabled (`Access-Control-Allow-Credentials: true`)
- **Methods**: All standard HTTP methods allowed
- **Headers**: Content-Type, Authorization, and others allowed

### Backend CORS Implementation
1. **API Gateway**: `cors: true` in serverless.yml
2. **Lambda Responses**: All include CORS headers
3. **S3 Bucket**: CORS rules configured for file uploads

## Common Issues and Solutions

### 1. CORS Errors in Browser

**Symptoms:**
- "Access to XMLHttpRequest... has been blocked by CORS policy"
- "No 'Access-Control-Allow-Origin' header is present"

**Solutions:**
- Ensure backend is running (`npm run offline`)
- Check that requests are going through Vite proxy (localhost:3000)
- Verify environment variables in `.env` file

### 2. Backend Not Running

**Symptoms:**
- ECONNREFUSED errors
- "Network Error" in browser console

**Solution:**
```bash
cd Biormika-AWS-Backend
npm install
npm run offline
```

### 3. Authentication Failures

**Symptoms:**
- 401 Unauthorized errors
- Token refresh not working

**Solutions:**
- Check JWT token expiration settings
- Verify Cognito configuration
- Test with fresh login

### 4. WebSocket Connection Issues

**Symptoms:**
- WebSocket connection failed
- No real-time updates

**Solutions:**
- Check WebSocket URL in `.env`
- Verify token is included in connection URL
- Check browser console for specific errors

## Testing Checklist

### ✅ Basic Connectivity
- [ ] Backend health check passes
- [ ] Frontend can reach backend through proxy
- [ ] No CORS errors in browser console

### ✅ Authentication Flow
- [ ] User can sign up
- [ ] User can log in
- [ ] Tokens are stored correctly
- [ ] Token refresh works
- [ ] Protected routes require authentication

### ✅ File Management
- [ ] File upload initialization works
- [ ] S3 presigned URLs are generated
- [ ] File validation endpoint responds
- [ ] File list updates correctly

### ✅ Analysis Workflow
- [ ] Analysis can be started
- [ ] Status polling works
- [ ] Results can be retrieved
- [ ] PDF reports can be downloaded

### ✅ WebSocket Updates
- [ ] Connection establishes with auth
- [ ] Progress updates received
- [ ] Connection reconnects on failure

## Environment Variables

Ensure these are set in `biormika-new-frontend/.env`:

```env
VITE_API_BASE_URL=http://localhost:3001
VITE_WEBSOCKET_URL=ws://localhost:3002
VITE_AWS_REGION=us-east-1
VITE_COGNITO_USER_POOL_ID=your-pool-id
VITE_COGNITO_CLIENT_ID=your-client-id
VITE_ENVIRONMENT=development
VITE_ENABLE_WEBSOCKET=true
VITE_ENABLE_DEBUG_MODE=true
```

## Production Deployment

For production, update CORS configuration:

1. **Restrict Origins**: Replace `*` with specific domains
2. **Enable HTTPS**: Use secure protocols only
3. **Update Environment**: Set production URLs
4. **Configure CloudFront**: If using CDN, configure header forwarding

## Debugging Tips

1. **Browser DevTools**:
   - Network tab: Check request/response headers
   - Console: Look for CORS or network errors
   - Application tab: Verify stored tokens

2. **Backend Logs**:
   ```bash
   # View serverless offline logs
   cd Biormika-AWS-Backend
   npm run offline
   ```

3. **Test Individual Endpoints**:
   ```bash
   # Test with curl
   curl -X POST http://localhost:3001/auth/login \
     -H "Content-Type: application/json" \
     -H "Origin: http://localhost:3000" \
     -d '{"email":"<EMAIL>","password":"password"}' \
     -v
   ```

## Support

If you encounter issues not covered here:
1. Check the browser console for specific error messages
2. Review the backend logs for Lambda function errors
3. Verify all services are running and configured correctly