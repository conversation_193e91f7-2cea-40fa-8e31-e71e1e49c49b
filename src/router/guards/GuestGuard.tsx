import type { ReactNode } from 'react'
import { Navigate, useLocation } from 'react-router-dom'

interface GuestGuardProps {
  children: ReactNode
}

import { useSelector } from 'react-redux'
import type { RootState } from '../../store'

const useAuth = () => {
  const { isAuthenticated, isLoading } = useSelector((state: RootState) => state.auth)
  return {
    isAuthenticated,
    isLoading,
  }
}

export function GuestGuard({ children }: GuestGuardProps) {
  const { isAuthenticated, isLoading } = useAuth()
  const location = useLocation()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-brand-primary"></div>
      </div>
    )
  }

  if (isAuthenticated) {
    // Redirect to the intended page or dashboard
    const from = (location.state as { from?: { pathname: string } })?.from?.pathname || '/'
    return <Navigate to={from} replace />
  }

  return <>{children}</>
}