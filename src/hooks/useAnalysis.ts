import { useCallback, useState, useEffect, useRef } from 'react'
import { useAppSelector } from '../store'
import { analysisService } from '../services/analysis.service'
import type { AnalysisSettings } from '../types/analysis'
import type { AnalysisStatus, AnalysisResult } from '../services/analysis.service'
import { showToast } from '../utils/toast'
import { useWebSocket } from '../contexts/useWebSocket'

interface UseAnalysisOptions {
  autoFetchResults?: boolean
  pollingInterval?: number
}

export function useAnalysis(options: UseAnalysisOptions = {}) {
  const { globalSettings, fileSpecificSettings } = useAppSelector(state => state.analysisSettings)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [currentAnalysis, setCurrentAnalysis] = useState<AnalysisStatus | null>(null)
  const [analysisResults, setAnalysisResults] = useState<Map<string, AnalysisResult>>(new Map())
  const [error, setError] = useState<string | null>(null)
  
  const { subscribeToAnalysisProgress, subscribeToAnalysisComplete } = useWebSocket()
  const analysisIdRef = useRef<string | null>(null)
  
  useEffect(() => {
    const unsubscribeProgress = subscribeToAnalysisProgress((data) => {
      if (data.analysisId === analysisIdRef.current) {
        setCurrentAnalysis(prev => prev ? {
          ...prev,
          progress: data.progress,
          currentStep: data.currentStep,
        } : null)
      }
    })
    
    const unsubscribeComplete = subscribeToAnalysisComplete(async (data) => {
      if (data.analysisId === analysisIdRef.current) {
        setIsAnalyzing(false)
        
        if (data.status === 'completed' && options.autoFetchResults !== false) {
          await fetchResults(data.analysisId)
        } else if (data.status === 'failed') {
          setError(data.error || 'Analysis failed')
        }
      }
    })
    
    return () => {
      unsubscribeProgress()
      unsubscribeComplete()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [subscribeToAnalysisProgress, subscribeToAnalysisComplete, options.autoFetchResults])
  
  const getSettingsForFile = useCallback((fileId: string): AnalysisSettings | null => {
    const settings = fileSpecificSettings[fileId] || globalSettings
    
    if (!settings) {
      showToast.error('Please configure analysis settings first')
      return null
    }
    
    return settings
  }, [globalSettings, fileSpecificSettings])
  
  const startAnalysis = useCallback(async (fileId: string, customSettings?: AnalysisSettings) => {
    const settings = customSettings || getSettingsForFile(fileId)
    
    if (!settings) {
      return null
    }
    
    setIsAnalyzing(true)
    setError(null)
    
    try {
      const response = await analysisService.startAnalysis(fileId, settings)
      analysisIdRef.current = response.analysisId
      
      const status: AnalysisStatus = {
        analysisId: response.analysisId,
        fileId,
        status: response.status,
        progress: 0,
        startedAt: new Date().toISOString(),
        estimatedTime: response.estimatedTime,
      }
      
      setCurrentAnalysis(status)
      
      if (!options.pollingInterval && import.meta.env.VITE_ENABLE_WEBSOCKET !== 'true') {
        analysisService.pollStatus(response.analysisId, {
          interval: options.pollingInterval || 2000,
          onProgress: (status) => {
            setCurrentAnalysis(status)
          },
        }).then(() => {
          setIsAnalyzing(false)
          if (options.autoFetchResults !== false) {
            fetchResults(response.analysisId)
          }
        }).catch((error) => {
          setIsAnalyzing(false)
          setError(error instanceof Error ? error.message : 'Analysis failed')
        })
      }
      
      return response.analysisId
    } catch (error) {
      setIsAnalyzing(false)
      setError(error instanceof Error ? error.message : 'Failed to start analysis')
      return null
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getSettingsForFile, options.pollingInterval, options.autoFetchResults])
  
  const fetchResults = useCallback(async (analysisId: string) => {
    try {
      const results = await analysisService.getResults(analysisId)
      setAnalysisResults(prev => new Map(prev).set(analysisId, results))
      return results
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to fetch results')
      return null
    }
  }, [])
  
  const downloadReport = useCallback(async (analysisId: string, fileName?: string) => {
    try {
      const defaultName = `analysis-report-${analysisId}.pdf`
      await analysisService.downloadReport(analysisId, fileName || defaultName)
    } catch {
      showToast.error('Failed to download report')
    }
  }, [])
  
  const getAnalysisStatus = useCallback(async (analysisId: string) => {
    try {
      const status = await analysisService.getStatus(analysisId)
      if (analysisId === analysisIdRef.current) {
        setCurrentAnalysis(status)
      }
      return status
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to get analysis status')
      return null
    }
  }, [])
  
  const cancelAnalysis = useCallback((analysisId: string) => {
    if (analysisId === analysisIdRef.current) {
      analysisService.stopPolling(analysisId)
      setIsAnalyzing(false)
      setCurrentAnalysis(null)
      analysisIdRef.current = null
    }
  }, [])
  
  const clearResults = useCallback(() => {
    setAnalysisResults(new Map())
    setCurrentAnalysis(null)
    setError(null)
    analysisIdRef.current = null
  }, [])
  
  return {
    isAnalyzing,
    currentAnalysis,
    analysisResults: Array.from(analysisResults.values()),
    error,
    startAnalysis,
    fetchResults,
    downloadReport,
    getAnalysisStatus,
    cancelAnalysis,
    clearResults,
    getResultById: (id: string) => analysisResults.get(id),
  }
}

export function useAnalysisSettings() {
  const { globalSettings, fileSpecificSettings, isConfigured } = useAppSelector(state => state.analysisSettings)
  const [isSaving, setIsSaving] = useState(false)
  
  const saveConfiguration = useCallback(async (settings: AnalysisSettings, name?: string) => {
    setIsSaving(true)
    
    try {
      await analysisService.saveConfiguration(settings, name)
      showToast.success('Configuration saved successfully')
      return true
    } catch {
      showToast.error('Failed to save configuration')
      return false
    } finally {
      setIsSaving(false)
    }
  }, [])
  
  return {
    globalSettings,
    fileSpecificSettings,
    isConfigured,
    isSaving,
    saveConfiguration,
  }
}