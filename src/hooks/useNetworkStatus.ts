import { useState, useEffect, useCallback } from 'react'
import { showOfflineToast, showOnlineToast } from '../utils/toast'

interface NetworkStatus {
  isOnline: boolean
  wasOffline: boolean
  downtime: number | null
}

export function useNetworkStatus() {
  const [networkStatus, setNetworkStatus] = useState<NetworkStatus>({
    isOnline: navigator.onLine,
    wasOffline: false,
    downtime: null,
  })
  
  const [offlineTimestamp, setOfflineTimestamp] = useState<number | null>(null)
  
  const handleOnline = useCallback(() => {
    const wasOffline = !networkStatus.isOnline
    let downtime: number | null = null
    
    if (offlineTimestamp) {
      downtime = Date.now() - offlineTimestamp
      setOfflineTimestamp(null)
    }
    
    setNetworkStatus({
      isOnline: true,
      wasOffline,
      downtime,
    })
    
    if (wasOffline) {
      showOnlineToast()
    }
  }, [networkStatus.isOnline, offlineTimestamp])
  
  const handleOffline = useCallback(() => {
    setNetworkStatus(prev => ({
      ...prev,
      isOnline: false,
      wasOffline: true,
    }))
    
    setOfflineTimestamp(Date.now())
    showOfflineToast()
  }, [])
  
  useEffect(() => {
    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)
    
    const intervalId = setInterval(() => {
      const isOnline = navigator.onLine
      
      if (isOnline !== networkStatus.isOnline) {
        if (isOnline) {
          handleOnline()
        } else {
          handleOffline()
        }
      }
    }, 5000)
    
    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
      clearInterval(intervalId)
    }
  }, [handleOnline, handleOffline, networkStatus.isOnline])
  
  const checkConnection = useCallback(async (): Promise<boolean> => {
    if (!navigator.onLine) {
      return false
    }
    
    try {
      const response = await fetch(`${window.location.origin}/favicon.ico`, {
        method: 'HEAD',
        cache: 'no-cache',
      })
      return response.ok
    } catch {
      return false
    }
  }, [])
  
  return {
    ...networkStatus,
    checkConnection,
  }
}

export function useOnlineStatus(): boolean {
  const { isOnline } = useNetworkStatus()
  return isOnline
}

export function useOfflineDetector(callback?: () => void) {
  const { isOnline, wasOffline } = useNetworkStatus()
  
  useEffect(() => {
    if (!isOnline && callback) {
      callback()
    }
  }, [isOnline, callback])
  
  return { isOnline, wasOffline }
}