import { useEffect, useCallback, useRef } from 'react'
import { useWebSocket as useWebSocketContext } from '../contexts/useWebSocket'
import type { 
  AnalysisProgressMessage, 
  AnalysisCompleteMessage, 
  FileValidatedMessage 
} from '../services/websocket.service'

interface WebSocketSubscription {
  unsubscribe: () => void
}

export function useWebSocketSubscription<T = unknown>(
  event: string,
  handler: (data: T) => void
): WebSocketSubscription {
  const { isConnected, subscribeToAnalysisProgress, subscribeToAnalysisComplete, subscribeToFileValidated } = useWebSocketContext()
  const handlerRef = useRef(handler)
  const unsubscribeRef = useRef<(() => void) | null>(null)
  
  useEffect(() => {
    handlerRef.current = handler
  }, [handler])
  
  useEffect(() => {
    if (!isConnected) return
    
    const currentHandler = (data: T) => handlerRef.current(data)
    
    if (event === 'analysisProgress') {
      unsubscribeRef.current = subscribeToAnalysisProgress(currentHandler as (data: AnalysisProgressMessage) => void)
    } else if (event === 'analysisComplete') {
      unsubscribeRef.current = subscribeToAnalysisComplete(currentHandler as (data: AnalysisCompleteMessage) => void)
    } else if (event === 'fileValidated') {
      unsubscribeRef.current = subscribeToFileValidated(currentHandler as (data: FileValidatedMessage) => void)
    }
    
    return () => {
      if (unsubscribeRef.current) {
        unsubscribeRef.current()
        unsubscribeRef.current = null
      }
    }
  }, [isConnected, event, subscribeToAnalysisProgress, subscribeToAnalysisComplete, subscribeToFileValidated])
  
  const unsubscribe = useCallback(() => {
    if (unsubscribeRef.current) {
      unsubscribeRef.current()
      unsubscribeRef.current = null
    }
  }, [])
  
  return { unsubscribe }
}

export function useAnalysisProgress(
  handler: (data: AnalysisProgressMessage) => void
) {
  return useWebSocketSubscription('analysisProgress', handler)
}

export function useAnalysisComplete(
  handler: (data: AnalysisCompleteMessage) => void
) {
  return useWebSocketSubscription('analysisComplete', handler)
}

export function useFileValidated(
  handler: (data: FileValidatedMessage) => void
) {
  return useWebSocketSubscription('fileValidated', handler)
}

export function useWebSocketStatus() {
  const { status, isConnected, connect, disconnect } = useWebSocketContext()
  
  return {
    status,
    isConnected,
    connect,
    disconnect,
  }
}

export { useWebSocket } from '../contexts/useWebSocket'