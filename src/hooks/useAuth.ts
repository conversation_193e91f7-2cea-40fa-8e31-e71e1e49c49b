import { useCallback, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { useAppDispatch, useAppSelector } from '../store'
import { login, logout, setLoading, setError, clearError } from '../store/slices/authSlice'
import { authService } from '../services/auth.service'
import type { LoginCredentials, RegisterData } from '../types/auth'
import { showToast } from '../utils/toast'

export function useAuth() {
  const dispatch = useAppDispatch()
  const navigate = useNavigate()
  const { user, isAuthenticated, isLoading, error, token } = useAppSelector(state => state.auth)
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const handleLogin = useCallback(async (credentials: LoginCredentials) => {
    setIsSubmitting(true)
    dispatch(setLoading(true))
    dispatch(clearError())
    
    try {
      const response = await authService.login(credentials)
      dispatch(login({
        user: response.user,
        token: response.token,
        refreshToken: response.refreshToken
      }))
      showToast.success('Login successful!')
      navigate('/dashboard')
    } catch (error) {
      const errorMessage = error instanceof Error && 'response' in error ? (error as {response?: {data?: {message?: string}}}).response?.data?.message || 'Login failed' : 'Login failed'
      dispatch(setError(errorMessage))
    } finally {
      setIsSubmitting(false)
      dispatch(setLoading(false))
    }
  }, [dispatch, navigate])
  
  const handleSignup = useCallback(async (data: RegisterData) => {
    setIsSubmitting(true)
    dispatch(setLoading(true))
    dispatch(clearError())
    
    try {
      const response = await authService.signup({
        name: data.name,
        email: data.email,
        password: data.password
      })
      dispatch(login({
        user: response.user,
        token: response.token,
        refreshToken: response.refreshToken
      }))
      showToast.success('Account created successfully!')
      navigate('/dashboard')
    } catch (error) {
      const errorMessage = error instanceof Error && 'response' in error ? (error as {response?: {data?: {message?: string}}}).response?.data?.message || 'Signup failed' : 'Signup failed'
      dispatch(setError(errorMessage))
    } finally {
      setIsSubmitting(false)
      dispatch(setLoading(false))
    }
  }, [dispatch, navigate])
  
  const handleLogout = useCallback(async () => {
    try {
      await authService.logout()
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      dispatch(logout())
      navigate('/login')
    }
  }, [dispatch, navigate])
  
  const handleForgotPassword = useCallback(async (email: string) => {
    setIsSubmitting(true)
    dispatch(clearError())
    
    try {
      await authService.forgotPassword(email)
      showToast.success('Password reset instructions sent to your email')
      return true
    } catch (error) {
      const errorMessage = error instanceof Error && 'response' in error ? (error as {response?: {data?: {message?: string}}}).response?.data?.message || 'Failed to send reset email' : 'Failed to send reset email'
      dispatch(setError(errorMessage))
      return false
    } finally {
      setIsSubmitting(false)
    }
  }, [dispatch])
  
  const handleResetPassword = useCallback(async (token: string, password: string, confirmPassword: string) => {
    setIsSubmitting(true)
    dispatch(clearError())
    
    try {
      await authService.resetPassword({ token, password, confirmPassword })
      showToast.success('Password reset successful')
      navigate('/login')
      return true
    } catch (error) {
      const errorMessage = error instanceof Error && 'response' in error ? (error as {response?: {data?: {message?: string}}}).response?.data?.message || 'Failed to reset password' : 'Failed to reset password'
      dispatch(setError(errorMessage))
      return false
    } finally {
      setIsSubmitting(false)
    }
  }, [dispatch, navigate])
  
  const handleGoogleLogin = useCallback(() => {
    authService.initiateGoogleAuth()
  }, [])
  
  const handleGithubLogin = useCallback(() => {
    authService.initiateGithubAuth()
  }, [])
  
  const handleOAuthCallback = useCallback(async (provider: 'google' | 'github', code: string, state: string) => {
    dispatch(setLoading(true))
    dispatch(clearError())
    
    try {
      const response = await authService.handleOAuthCallback(provider, code, state)
      dispatch(login({
        user: response.user,
        token: response.token,
        refreshToken: response.refreshToken
      }))
      showToast.success(`${provider} login successful!`)
      navigate('/dashboard')
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : `${provider} login failed`
      dispatch(setError(errorMessage))
      navigate('/login')
    } finally {
      dispatch(setLoading(false))
    }
  }, [dispatch, navigate])
  
  const checkAuth = useCallback(async () => {
    if (!token) return false
    
    try {
      const isValid = await authService.validateToken()
      if (!isValid) {
        dispatch(logout())
        return false
      }
      return true
    } catch {
      dispatch(logout())
      return false
    }
  }, [token, dispatch])
  
  return {
    user,
    isAuthenticated,
    isLoading,
    isSubmitting,
    error,
    login: handleLogin,
    signup: handleSignup,
    logout: handleLogout,
    forgotPassword: handleForgotPassword,
    resetPassword: handleResetPassword,
    googleLogin: handleGoogleLogin,
    githubLogin: handleGithubLogin,
    handleOAuthCallback,
    checkAuth,
    clearError: () => dispatch(clearError()),
  }
}