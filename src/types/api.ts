export interface ApiResponse<T = unknown> {
  success: boolean
  data: T
  message?: string
  error?: string
}

export interface AuthLoginRequest {
  email: string
  password: string
}

export interface AuthLoginResponse {
  user: {
    id: string
    email: string
    name: string
    avatar?: string
    role: string
    permissions: string[]
  }
  token: string
  refreshToken: string
}

export interface AuthSignupRequest {
  name: string
  email: string
  password: string
}

export interface AuthSignupResponse {
  user: {
    id: string
    email: string
    name: string
    role: string
    permissions: string[]
  }
  token: string
  refreshToken: string
}

export interface AuthRefreshRequest {
  refreshToken: string
}

export interface AuthRefreshResponse {
  token: string
  refreshToken?: string
}

export interface ForgotPasswordRequest {
  email: string
}

export interface ResetPasswordRequest {
  token: string
  password: string
  confirmPassword: string
}

export interface SocialAuthRequest {
  provider: 'google' | 'github'
  accessToken: string
}

export interface FileUploadInitRequest {
  fileName: string
  fileSize: number
  contentType: string
}

export interface FileUploadInitResponse {
  fileId: string
  uploadUrl: string
  uploadFields?: Record<string, string>
}

export interface FileValidateResponse {
  fileId: string
  status: 'valid' | 'invalid' | 'error'
  message?: string
  metadata?: {
    channels: string[]
    duration: number
    sampleRate: number
  }
}

export interface AnalysisStartRequest {
  fileId: string
  settings: {
    thresholdSettings: {
      amplitude1: number
      amplitude2: number
      peaks1: number
      peaks2: number
      duration: number
    }
    frequencyFilterSettings: {
      lowCutoffFilter: number
      highCutoffFilter: number
    }
    montageType: 'bipolar' | 'average' | 'referential'
  }
}

export interface AnalysisStartResponse {
  analysisId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  estimatedTime?: number
}

export interface AnalysisStatusResponse {
  analysisId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  currentStep?: string
  error?: string
}

export interface AnalysisResultsResponse {
  analysisId: string
  fileId: string
  results: {
    hfoCount: number
    channels: Record<string, {
      hfoCount: number
      hfoRate: number
      hfoDuration: number
      hfoAmplitude: number
    }>
    summary: {
      totalHFOs: number
      averageRate: number
      channelDistribution: Record<string, number>
      temporalDistribution: number[]
    }
  }
  reportUrl?: string
  downloadUrl?: string
}