import type { AxiosResponse, AxiosError } from 'axios'

// Custom Axios response type
export interface ApiAxiosResponse<T = unknown> extends AxiosResponse<T> {
  data: T
}

// Custom Axios error type
export interface ApiAxiosError<T = unknown> extends AxiosError<T> {
  response?: AxiosResponse<T>
}

// Request configuration
export interface RequestConfig {
  showSuccessToast?: boolean
  showErrorToast?: boolean
  successMessage?: string
  skipAuthRefresh?: boolean
}

// Interceptor types
export interface RequestInterceptorConfig {
  onRequest?: (config: import('axios').InternalAxiosRequestConfig) => import('axios').InternalAxiosRequestConfig
  onRequestError?: (error: unknown) => Promise<unknown>
}

export interface ResponseInterceptorConfig {
  onResponse?: (response: ApiAxiosResponse) => ApiAxiosResponse
  onResponseError?: (error: ApiAxiosError) => Promise<unknown>
}