import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import type {
  AnalysisSettings,
  AnalysisSettingsState,
  AnalysisSettingsPayload,
} from '../../types/analysis'

const defaultAnalysisSettings: AnalysisSettings = {
  thresholdSettings: {
    amplitude1: 2,
    amplitude2: 5,
    peaks1: 6,
    peaks2: 5,
    duration: 10,
  },
  synchronizationSettings: {
    temporalSync: 10,
    spatialSync: 10,
  },
  montageSelection: {
    bipolar: true,
    average: false,
    referential: false,
  },
  frequencyFilterSettings: {
    lowCutoffFilter: 50,
    highCutoffFilter: 500,
  },
  segmentSelectionSettings: {
    lowCutoffFilter: 50,
    highCutoffFilter: 500,
    entireFile: true,
    startEndTimes: false,
    startTimeDuration: false,
  },
}

const initialState: AnalysisSettingsState = {
  globalSettings: null,
  fileSpecificSettings: {},
  isConfigured: false,
}

export const analysisSettingsSlice = createSlice({
  name: 'analysisSettings',
  initialState,
  reducers: {
    setGlobalSettings: (state, action: PayloadAction<AnalysisSettings>) => {
      state.globalSettings = action.payload
      state.isConfigured = true
      state.lastConfigured = new Date()
    },

    setFileSpecificSettings: (state, action: PayloadAction<AnalysisSettingsPayload>) => {
      if (action.payload.fileId) {
        state.fileSpecificSettings[action.payload.fileId] = action.payload.settings
      }
    },

    removeFileSpecificSettings: (state, action: PayloadAction<string>) => {
      delete state.fileSpecificSettings[action.payload]
    },

    resetToDefaultSettings: (state) => {
      state.globalSettings = defaultAnalysisSettings
      state.isConfigured = true
      state.lastConfigured = new Date()
    },

    clearAllSettings: (state) => {
      state.globalSettings = null
      state.fileSpecificSettings = {}
      state.isConfigured = false
      state.lastConfigured = undefined
    },

    updateThresholdSettings: (state, action: PayloadAction<Partial<typeof defaultAnalysisSettings.thresholdSettings>>) => {
      if (state.globalSettings) {
        state.globalSettings.thresholdSettings = {
          ...state.globalSettings.thresholdSettings,
          ...action.payload,
        }
      }
    },

    updateSynchronizationSettings: (state, action: PayloadAction<Partial<typeof defaultAnalysisSettings.synchronizationSettings>>) => {
      if (state.globalSettings) {
        state.globalSettings.synchronizationSettings = {
          ...state.globalSettings.synchronizationSettings,
          ...action.payload,
        }
      }
    },

    updateMontageSelection: (state, action: PayloadAction<Partial<typeof defaultAnalysisSettings.montageSelection>>) => {
      if (state.globalSettings) {
        state.globalSettings.montageSelection = {
          ...state.globalSettings.montageSelection,
          ...action.payload,
        }
      }
    },

    updateFrequencyFilterSettings: (state, action: PayloadAction<Partial<typeof defaultAnalysisSettings.frequencyFilterSettings>>) => {
      if (state.globalSettings) {
        state.globalSettings.frequencyFilterSettings = {
          ...state.globalSettings.frequencyFilterSettings,
          ...action.payload,
        }
      }
    },

    updateSegmentSelectionSettings: (state, action: PayloadAction<Partial<typeof defaultAnalysisSettings.segmentSelectionSettings>>) => {
      if (state.globalSettings) {
        state.globalSettings.segmentSelectionSettings = {
          ...state.globalSettings.segmentSelectionSettings,
          ...action.payload,
        }
      }
    },
  },
})

export const {
  setGlobalSettings,
  setFileSpecificSettings,
  removeFileSpecificSettings,
  resetToDefaultSettings,
  clearAllSettings,
  updateThresholdSettings,
  updateSynchronizationSettings,
  updateMontageSelection,
  updateFrequencyFilterSettings,
  updateSegmentSelectionSettings,
} = analysisSettingsSlice.actions

export { defaultAnalysisSettings }
export default analysisSettingsSlice.reducer