import { createSlice } from '@reduxjs/toolkit'
import type { PayloadAction } from '@reduxjs/toolkit'
import { FileStatus } from '../../types/files'
import type {
  FileUpload,
  FilesState,
  AddFilesPayload,
  UpdateFileStatusPayload,
  UpdateFileProgressPayload,
} from '../../types/files'

const initialState: FilesState = {
  files: [],
  isUploading: false,
  totalFiles: 0,
  completedFiles: 0,
}

export const filesSlice = createSlice({
  name: 'files',
  initialState,
  reducers: {
    addFiles: (state, action: PayloadAction<AddFilesPayload>) => {
      const newFiles: FileUpload[] = action.payload.files.map((file) => ({
        id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        file,
        name: file.name,
        size: file.size,
        type: file.type,
        status: FileStatus.PENDING,
        uploadProgress: 0,
        uploadedAt: new Date(),
      }))

      state.files.push(...newFiles)
      state.totalFiles = state.files.length
    },

    removeFile: (state, action: PayloadAction<string>) => {
      state.files = state.files.filter((file) => file.id !== action.payload)
      state.totalFiles = state.files.length
      state.completedFiles = state.files.filter(
        (file) => file.status === FileStatus.VALID || file.status === FileStatus.COMPLETED
      ).length
    },

    updateFileStatus: (state, action: PayloadAction<UpdateFileStatusPayload>) => {
      const file = state.files.find((f) => f.id === action.payload.id)
      if (file) {
        file.status = action.payload.status
        if (action.payload.errorMessage) {
          file.errorMessage = action.payload.errorMessage
        }

        if (
          action.payload.status === FileStatus.VALID ||
          action.payload.status === FileStatus.COMPLETED
        ) {
          file.uploadProgress = 100
        }
      }

      state.completedFiles = state.files.filter(
        (file) => file.status === FileStatus.VALID || file.status === FileStatus.COMPLETED
      ).length
    },

    updateFileProgress: (state, action: PayloadAction<UpdateFileProgressPayload>) => {
      const file = state.files.find((f) => f.id === action.payload.id)
      if (file) {
        file.uploadProgress = action.payload.progress
        if (action.payload.progress >= 100) {
          file.status = FileStatus.VALIDATING
        }
      }
    },

    setIsUploading: (state, action: PayloadAction<boolean>) => {
      state.isUploading = action.payload
    },

    clearAllFiles: (state) => {
      state.files = []
      state.totalFiles = 0
      state.completedFiles = 0
      state.isUploading = false
    },

    startFileValidation: (state, action: PayloadAction<string>) => {
      const file = state.files.find((f) => f.id === action.payload)
      if (file) {
        file.status = FileStatus.VALIDATING
      }
    },
  },
})

export const {
  addFiles,
  removeFile,
  updateFileStatus,
  updateFileProgress,
  setIsUploading,
  clearAllFiles,
  startFileValidation,
} = filesSlice.actions

export default filesSlice.reducer
