import { useState } from 'react'
import { Form } from 'antd'
import { WaveBackground } from '../components/WaveBackground'
import { BrandHeader } from '../components/BrandHeader'
import { FormField } from '../components/FormField'
import { PrimaryButton } from '../components/PrimaryButton'

interface RequestDemoForm {
  fullName: string
  email: string
  role: string
  organization?: string
}

const roleOptions = [
  { value: 'researcher', label: 'Researcher' },
  { value: 'clinician', label: 'Clinician' },
  { value: 'data-scientist', label: 'Data Scientist' },
  { value: 'neuroscientist', label: 'Neuroscientist' },
  { value: 'student', label: 'Student' },
  { value: 'other', label: 'Other' }
]

export default function RequestDemo() {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)

  const handleSubmit = async (values: RequestDemoForm) => {
    setLoading(true)
    try {
      console.log('Request demo form values:', values)
    } catch (error) {
      console.error('Request demo error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      <WaveBackground variant="dark" />
      
      <div className="absolute top-6 left-6 z-20 flex items-center gap-3">
        <img 
          src="/logo/biormika_icon.png" 
          alt="Biormika" 
          className="w-8 h-8"
        />
        <h1 className="text-2xl font-bold text-white">Biormika</h1>
      </div>
      
      <div className="relative z-10 min-h-screen flex">
        <div className="flex-1 flex items-center justify-center px-8">
          <div className="max-w-lg text-white">
            <h1 className="text-5xl font-bold mb-6 leading-tight">
              Decode the Mind.<br />
              In Real Time.
            </h1>
            <p className="text-lg text-gray-200 leading-relaxed">
              Experience seamless EEG data visualization designed<br />
              for clinical speed and clarity. Request your demo today.
            </p>
          </div>
        </div>

        <div className="flex-1 flex items-center justify-center px-8">
          <div className="w-full max-w-md">
            <div className="bg-white rounded-2xl shadow-2xl p-8">
              <BrandHeader />

              <div className="mb-6">
                <h3 className="text-xl font-semibold text-gray-900 text-center">Request Demo</h3>
              </div>

              <Form
                form={form}
                layout="vertical"
                onFinish={handleSubmit}
                className="space-y-4"
              >
                <FormField
                  label="Full Name"
                  name="fullName"
                  placeholder="Full Name"
                  required
                  rules={[{ min: 2, message: 'Full name must be at least 2 characters!' }]}
                />

                <FormField
                  label="Email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  required
                />

                <FormField
                  label="Role"
                  name="role"
                  type="select"
                  placeholder="Role"
                  options={roleOptions}
                  required
                />

                <FormField
                  label="Organization (optional)"
                  name="organization"
                  placeholder="Organization"
                />

                <div className="pt-4">
                  <Form.Item>
                    <PrimaryButton htmlType="submit" loading={loading}>
                      Request Demo
                    </PrimaryButton>
                  </Form.Item>
                </div>
              </Form>
            </div>
          </div>
        </div>
      </div>

      <div className="absolute bottom-6 left-1/2 transform -translate-x-1/2 text-center text-gray-400 text-xs z-20">
        <span>Privacy Policy | Terms & Conditions | © All Rights Reserved 2025</span>
      </div>
    </div>
  )
}