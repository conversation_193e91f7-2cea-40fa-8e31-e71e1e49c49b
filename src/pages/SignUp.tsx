import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { Form } from 'antd'
import { AuthLayout } from '../components/AuthLayout'
import { AuthCard } from '../components/AuthCard'
import { BrandHeader } from '../components/BrandHeader'
import { FormField } from '../components/FormField'
import { PrimaryButton } from '../components/PrimaryButton'
import { SocialButton } from '../components/SocialButton'
import { useAuth } from '../hooks/useAuth'
import type { RegisterData } from '../types/auth'

export default function SignUp() {
  const [form] = Form.useForm()
  const { isLoading, isSubmitting, error, signup, googleLogin, githubLogin, clearError } = useAuth()

  React.useEffect(() => {
    clearError()
  }, [clearError])

  const handleSubmit = async (values: RegisterData & { fullName: string }) => {
    await signup({
      name: values.fullName,
      email: values.email,
      password: values.password,
      confirmPassword: values.confirmPassword,
    })
  }

  return (
    <AuthLayout>
      <AuthCard width="lg">
        <BrandHeader />

        <div className="mb-6">
          <h2 className="text-xl font-semibold text-text-primary text-center">Create your account</h2>
        </div>

        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          className="space-y-4"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Full Name"
              name="fullName"
              placeholder="Full Name"
              required
              rules={[{ min: 2, message: 'Full name must be at least 2 characters!' }]}
            />

            <FormField
              label="Email"
              name="email"
              type="email"
              placeholder="<EMAIL>"
              required
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Password"
              name="password"
              type="password"
              placeholder="Password"
              required
              rules={[{ min: 8, message: 'Password must be at least 8 characters!' }]}
            />

            <FormField
              label="Re-Type Password"
              name="confirmPassword"
              type="password"
              placeholder="Password"
              required
              dependencies={['password']}
              rules={[
                ({ getFieldValue }: { getFieldValue: (name: string) => string }) => ({
                  validator(_: unknown, value: string) {
                    if (!value || getFieldValue('password') === value) {
                      return Promise.resolve()
                    }
                    return Promise.reject(new Error('The passwords do not match!'))
                  },
                }),
              ]}
            />
          </div>

          {error && (
            <div className="text-error text-sm text-center mt-2">
              {error}
            </div>
          )}

          <div className="pt-6">
            <Form.Item>
              <PrimaryButton htmlType="submit" loading={isLoading || isSubmitting}>
                Create Account
              </PrimaryButton>
            </Form.Item>
          </div>
        </Form>

        <div className="my-6 text-center">
          <span className="text-text-muted text-sm">Or</span>
        </div>

        <div className="space-y-3">
          <SocialButton 
            provider="google" 
            onClick={googleLogin}
            className={isLoading || isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
          >
            Sign up with Google
          </SocialButton>

          <SocialButton 
            provider="github" 
            onClick={githubLogin}
            className={isLoading || isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
          >
            Sign up with GitHub
          </SocialButton>
        </div>

        <div className="mt-8 text-center">
          <span className="text-text-secondary text-sm">
            Already have an account?{' '}
            <Link to="/login" className="text-brand-primary hover:text-brand-primary-dark font-medium">
              Sign in
            </Link>
          </span>
        </div>
      </AuthCard>
    </AuthLayout>
  )
}