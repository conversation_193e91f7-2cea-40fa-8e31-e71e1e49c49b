import { brandColors } from '../constants/colors'

export function PageFooter() {
  return (
    <footer className="mt-auto py-6 text-center">
      <div 
        className="flex items-center justify-center gap-3 text-sm"
        style={{ color: brandColors.text.secondary }}
      >
        <button 
          className="transition-colors"
          onMouseEnter={(e) => {
            e.currentTarget.style.color = brandColors.text.primary
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.color = brandColors.text.secondary
          }}
        >
          Privacy Policy
        </button>
        <span>|</span>
        <button 
          className="transition-colors"
          onMouseEnter={(e) => {
            e.currentTarget.style.color = brandColors.text.primary
          }}
          onMouseLeave={(e) => {
            e.currentTarget.style.color = brandColors.text.secondary
          }}
        >
          Terms & Conditions
        </button>
        <span>|</span>
        <span>© All Rights Reserved 2025</span>
      </div>
    </footer>
  )
}