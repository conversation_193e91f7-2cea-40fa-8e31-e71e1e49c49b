interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large'
  message?: string
  className?: string
}

export function LoadingSpinner({ 
  size = 'medium', 
  message,
  className = '' 
}: LoadingSpinnerProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'w-48 h-16'
      case 'large':
        return 'w-120 h-40'
      default: // medium
        return 'w-96 h-32'
    }
  }

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="mb-8">
        <img 
          src="/assets/wave.png" 
          alt="Brain wave animation" 
          className={`${getSizeClasses()} mx-auto object-contain animate-pulse`}
        />
      </div>
      {message && (
        <p className="text-lg text-gray-600 text-center">
          {message}
        </p>
      )}
    </div>
  )
}