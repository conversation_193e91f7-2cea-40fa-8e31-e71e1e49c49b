import { configureStore } from '@reduxjs/toolkit'
import { combineReducers } from '@reduxjs/toolkit'
import { persistStore, persistReducer } from 'redux-persist'
import storage from 'redux-persist/lib/storage'
import authSlice from './store/slices/authSlice'
import filesSlice from './store/slices/filesSlice'
import analysisSettingsSlice from './store/slices/analysisSettingsSlice'

// Centralized persistence whitelist - only these reducers will be persisted
const PERSISTED_REDUCERS = ['auth', 'analysisSettings']

const persistConfig = {
  key: 'root',
  storage,
  whitelist: PERSISTED_REDUCERS,
}

// All reducers in one place
const rootReducer = combineReducers({
  auth: authSlice,
  files: filesSlice,
  analysisSettings: analysisSettingsSlice,
})

const persistedReducer = persistReducer(persistConfig, rootReducer)

export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
})

export const persistor = persistStore(store)

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch

// Typed hooks
import { useDispatch, useSelector } from 'react-redux'
export const useAppDispatch = () => useDispatch<AppDispatch>()
export const useAppSelector = <T>(selector: (state: RootState) => T) => useSelector(selector)
