import { apiClient } from '../lib/axios'
import { API_ENDPOINTS } from '../constants/api'
import type {
  ApiResponse,
  AnalysisStartRequest,
  AnalysisStartResponse,
  AnalysisStatusResponse,
  AnalysisResultsResponse
} from '../types/api'
import type { AnalysisSettings } from '../types/analysis'

export interface AnalysisStatus {
  analysisId: string
  fileId: string
  status: 'pending' | 'processing' | 'completed' | 'failed'
  progress: number
  currentStep?: string
  error?: string
  startedAt: string
  completedAt?: string
  estimatedTime?: number
}

export interface AnalysisResult {
  analysisId: string
  fileId: string
  status: 'completed' | 'failed'
  results?: {
    hfoCount: number
    channels: Record<string, {
      hfoCount: number
      hfoRate: number
      hfoDuration: number
      hfoAmplitude: number
    }>
    summary: {
      totalHFOs: number
      averageRate: number
      channelDistribution: Record<string, number>
      temporalDistribution: number[]
    }
    statistics: {
      totalHFOs: number
      averageRate: number
      channelWithMostHFOs: string
      duration: number
    }
  }
  reportUrl?: string
  downloadUrl?: string
  error?: string
}

interface PollOptions {
  interval?: number
  maxAttempts?: number
  onProgress?: (status: AnalysisStatus) => void
}

class AnalysisService {
  private pollingIntervals: Map<string, NodeJS.Timeout> = new Map()
  private statusCache: Map<string, AnalysisStatus> = new Map()
  
  async startAnalysis(fileId: string, settings: AnalysisSettings): Promise<AnalysisStartResponse> {
    const request: AnalysisStartRequest = {
      fileId,
      settings: {
        thresholdSettings: settings.thresholdSettings,
        frequencyFilterSettings: settings.frequencyFilterSettings,
        montageType: this.getMontageType(settings.montageSelection)
      }
    }
    
    const response = await apiClient.post<ApiResponse<AnalysisStartResponse>>(
      API_ENDPOINTS.analysis.start,
      request,
      { showErrorToast: true }
    )
    
    const result = response.data.data
    
    this.statusCache.set(result.analysisId, {
      analysisId: result.analysisId,
      fileId,
      status: result.status,
      progress: 0,
      startedAt: new Date().toISOString(),
      estimatedTime: result.estimatedTime
    })
    
    return result
  }
  
  async getStatus(analysisId: string): Promise<AnalysisStatus> {
    const response = await apiClient.get<ApiResponse<AnalysisStatusResponse>>(
      API_ENDPOINTS.analysis.status(analysisId),
      { showErrorToast: false }
    )
    
    const data = response.data.data
    
    const status: AnalysisStatus = {
      analysisId: data.analysisId,
      fileId: this.statusCache.get(analysisId)?.fileId || '',
      status: data.status,
      progress: data.progress,
      currentStep: data.currentStep,
      error: data.error,
      startedAt: this.statusCache.get(analysisId)?.startedAt || new Date().toISOString(),
      completedAt: data.status === 'completed' || data.status === 'failed' 
        ? new Date().toISOString() 
        : undefined
    }
    
    this.statusCache.set(analysisId, status)
    
    return status
  }
  
  async getResults(analysisId: string): Promise<AnalysisResult> {
    const response = await apiClient.get<ApiResponse<AnalysisResultsResponse>>(
      API_ENDPOINTS.analysis.results(analysisId),
      { showErrorToast: true }
    )
    
    const data = response.data.data
    
    return {
      analysisId: data.analysisId,
      fileId: data.fileId,
      status: 'completed',
      results: {
        ...data.results,
        statistics: this.calculateStatistics(data.results)
      },
      reportUrl: data.reportUrl,
      downloadUrl: data.downloadUrl
    }
  }
  
  async downloadReport(analysisId: string, fileName: string): Promise<void> {
    const response = await apiClient.get(
      API_ENDPOINTS.analysis.download(analysisId),
      { 
        responseType: 'blob',
        showErrorToast: true 
      }
    )
    
    const url = window.URL.createObjectURL(new Blob([response.data]))
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', fileName)
    document.body.appendChild(link)
    link.click()
    link.remove()
    window.URL.revokeObjectURL(url)
  }
  
  async pollStatus(
    analysisId: string, 
    options: PollOptions = {}
  ): Promise<AnalysisStatus> {
    const {
      interval = 2000,
      maxAttempts = 300,
      onProgress
    } = options
    
    let attempts = 0
    
    return new Promise((resolve, reject) => {
      const poll = async () => {
        try {
          attempts++
          
          const status = await this.getStatus(analysisId)
          
          if (onProgress) {
            onProgress(status)
          }
          
          if (status.status === 'completed') {
            this.stopPolling(analysisId)
            resolve(status)
          } else if (status.status === 'failed') {
            this.stopPolling(analysisId)
            reject(new Error(status.error || 'Analysis failed'))
          } else if (attempts >= maxAttempts) {
            this.stopPolling(analysisId)
            reject(new Error('Analysis timeout - exceeded maximum polling attempts'))
          }
        } catch (error) {
          this.stopPolling(analysisId)
          reject(error)
        }
      }
      
      poll()
      
      const intervalId = setInterval(poll, interval)
      this.pollingIntervals.set(analysisId, intervalId)
    })
  }
  
  stopPolling(analysisId: string): void {
    const intervalId = this.pollingIntervals.get(analysisId)
    if (intervalId) {
      clearInterval(intervalId)
      this.pollingIntervals.delete(analysisId)
    }
  }
  
  stopAllPolling(): void {
    this.pollingIntervals.forEach((intervalId) => {
      clearInterval(intervalId)
    })
    this.pollingIntervals.clear()
  }
  
  getCachedStatus(analysisId: string): AnalysisStatus | undefined {
    return this.statusCache.get(analysisId)
  }
  
  clearCache(): void {
    this.statusCache.clear()
  }
  
  async saveConfiguration(settings: AnalysisSettings, name?: string): Promise<void> {
    await apiClient.post(
      API_ENDPOINTS.analysis.configure,
      { settings, name },
      { 
        showSuccessToast: true,
        successMessage: 'Analysis configuration saved successfully'
      }
    )
  }
  
  private getMontageType(montageSelection: AnalysisSettings['montageSelection']): 'bipolar' | 'average' | 'referential' {
    if (montageSelection.bipolar) return 'bipolar'
    if (montageSelection.average) return 'average'
    return 'referential'
  }
  
  private calculateStatistics(results: { channels?: Record<string, { hfoCount: number }> }): AnalysisResult['results']['statistics'] {
    const channels = Object.keys(results.channels || {})
    let totalHFOs = 0
    let channelWithMostHFOs = ''
    let maxHFOs = 0
    
    channels.forEach(channel => {
      const hfoCount = results.channels[channel]?.hfoCount || 0
      totalHFOs += hfoCount
      
      if (hfoCount > maxHFOs) {
        maxHFOs = hfoCount
        channelWithMostHFOs = channel
      }
    })
    
    const duration = results.summary?.duration || 1
    const averageRate = totalHFOs / duration
    
    return {
      totalHFOs,
      averageRate,
      channelWithMostHFOs,
      duration
    }
  }
}

export const analysisService = new AnalysisService()