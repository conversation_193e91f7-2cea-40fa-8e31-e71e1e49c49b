import { WEBSOCKET_URL, WEBSOCKET_EVENTS } from '../constants/api'
import { tokenStorage } from '../utils/token'
import { showToast } from '../utils/toast'

export type WebSocketStatus = 'connecting' | 'connected' | 'disconnected' | 'error'

export interface WebSocketMessage {
  event: string
  data: unknown
  timestamp: string
}

export interface AnalysisProgressMessage {
  analysisId: string
  progress: number
  currentStep: string
  estimatedTimeRemaining?: number
}

export interface AnalysisCompleteMessage {
  analysisId: string
  fileId: string
  status: 'completed' | 'failed'
  error?: string
  resultsUrl?: string
}

export interface FileValidatedMessage {
  fileId: string
  status: 'valid' | 'invalid'
  metadata?: {
    channels: string[]
    duration: number
    sampleRate: number
  }
  error?: string
}

type MessageHandler<T = unknown> = (data: T) => void
type ConnectionHandler = (status: WebSocketStatus) => void

interface ReconnectOptions {
  maxAttempts: number
  interval: number
  backoffMultiplier: number
}

class WebSocketService {
  private ws: WebSocket | null = null
  private status: WebSocketStatus = 'disconnected'
  private reconnectAttempts = 0
  private reconnectTimeout: NodeJS.Timeout | null = null
  private pingInterval: NodeJS.Timeout | null = null
  private messageHandlers: Map<string, Set<MessageHandler>> = new Map()
  private connectionHandlers: Set<ConnectionHandler> = new Set()
  private reconnectOptions: ReconnectOptions = {
    maxAttempts: 10,
    interval: 1000,
    backoffMultiplier: 1.5
  }
  private messageQueue: WebSocketMessage[] = []
  
  connect(): void {
    if (this.ws && (this.ws.readyState === WebSocket.CONNECTING || this.ws.readyState === WebSocket.OPEN)) {
      return
    }
    
    const token = tokenStorage.getAccessToken()
    if (!token) {
      this.updateStatus('error')
      showToast.error('Authentication required for WebSocket connection')
      return
    }
    
    this.updateStatus('connecting')
    
    try {
      const wsUrl = `${WEBSOCKET_URL}?token=${encodeURIComponent(token)}`
      this.ws = new WebSocket(wsUrl)
      
      this.ws.onopen = this.handleOpen.bind(this)
      this.ws.onmessage = this.handleMessage.bind(this)
      this.ws.onerror = this.handleError.bind(this)
      this.ws.onclose = this.handleClose.bind(this)
    } catch (error) {
      console.error('WebSocket connection error:', error)
      this.updateStatus('error')
      this.scheduleReconnect()
    }
  }
  
  disconnect(): void {
    this.clearReconnectTimeout()
    this.clearPingInterval()
    this.reconnectAttempts = 0
    
    if (this.ws) {
      this.ws.onclose = null
      this.ws.close()
      this.ws = null
    }
    
    this.updateStatus('disconnected')
  }
  
  send(event: string, data: unknown): void {
    const message: WebSocketMessage = {
      event,
      data,
      timestamp: new Date().toISOString()
    }
    
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(message))
    } else {
      this.messageQueue.push(message)
    }
  }
  
  on<T = unknown>(event: string, handler: MessageHandler<T>): void {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, new Set())
    }
    this.messageHandlers.get(event)!.add(handler)
  }
  
  off<T = unknown>(event: string, handler: MessageHandler<T>): void {
    const handlers = this.messageHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.messageHandlers.delete(event)
      }
    }
  }
  
  onConnectionChange(handler: ConnectionHandler): void {
    this.connectionHandlers.add(handler)
  }
  
  offConnectionChange(handler: ConnectionHandler): void {
    this.connectionHandlers.delete(handler)
  }
  
  getStatus(): WebSocketStatus {
    return this.status
  }
  
  isConnected(): boolean {
    return this.status === 'connected' && this.ws?.readyState === WebSocket.OPEN
  }
  
  private handleOpen(): void {
    console.log('WebSocket connected')
    this.updateStatus('connected')
    this.reconnectAttempts = 0
    this.clearReconnectTimeout()
    this.startPingInterval()
    this.flushMessageQueue()
  }
  
  private handleMessage(event: MessageEvent): void {
    try {
      const message: WebSocketMessage = JSON.parse(event.data)
      
      if (message.event === 'pong') {
        return
      }
      
      const handlers = this.messageHandlers.get(message.event)
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message.data)
          } catch (error) {
            console.error(`Error in WebSocket handler for ${message.event}:`, error)
          }
        })
      }
      
      this.handleSpecificEvents(message)
    } catch (error) {
      console.error('Error parsing WebSocket message:', error)
    }
  }
  
  private handleError(event: Event): void {
    console.error('WebSocket error:', event)
    this.updateStatus('error')
  }
  
  private handleClose(event: CloseEvent): void {
    console.log('WebSocket closed:', event.code, event.reason)
    this.updateStatus('disconnected')
    this.clearPingInterval()
    
    if (!event.wasClean && event.code !== 1000) {
      this.scheduleReconnect()
    }
  }
  
  private handleSpecificEvents(message: WebSocketMessage): void {
    switch (message.event) {
      case WEBSOCKET_EVENTS.analysisProgress:
        break
      case WEBSOCKET_EVENTS.analysisComplete: {
        const completeData = message.data as AnalysisCompleteMessage
        if (completeData.status === 'completed') {
          showToast.success('Analysis completed successfully!')
        } else {
          showToast.error(`Analysis failed: ${completeData.error || 'Unknown error'}`)
        }
        break
      }
      case WEBSOCKET_EVENTS.fileValidated: {
        const validatedData = message.data as FileValidatedMessage
        if (validatedData.status === 'valid') {
          showToast.success('File validated successfully')
        } else {
          showToast.error(`File validation failed: ${validatedData.error || 'Invalid file format'}`)
        }
        break
      }
      case WEBSOCKET_EVENTS.fileError:
        showToast.error(`File error: ${message.data.error || 'Unknown error'}`)
        break
    }
  }
  
  private updateStatus(status: WebSocketStatus): void {
    this.status = status
    this.connectionHandlers.forEach(handler => {
      try {
        handler(status)
      } catch (error) {
        console.error('Error in connection status handler:', error)
      }
    })
  }
  
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.reconnectOptions.maxAttempts) {
      console.error('Max reconnection attempts reached')
      this.updateStatus('error')
      showToast.error('Unable to establish WebSocket connection')
      return
    }
    
    this.clearReconnectTimeout()
    
    const delay = Math.min(
      this.reconnectOptions.interval * Math.pow(this.reconnectOptions.backoffMultiplier, this.reconnectAttempts),
      30000
    )
    
    this.reconnectAttempts++
    console.log(`Scheduling reconnect attempt ${this.reconnectAttempts} in ${delay}ms`)
    
    this.reconnectTimeout = setTimeout(() => {
      this.connect()
    }, delay)
  }
  
  private clearReconnectTimeout(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
  }
  
  private startPingInterval(): void {
    this.clearPingInterval()
    
    this.pingInterval = setInterval(() => {
      if (this.isConnected()) {
        this.send('ping', {})
      }
    }, 30000)
  }
  
  private clearPingInterval(): void {
    if (this.pingInterval) {
      clearInterval(this.pingInterval)
      this.pingInterval = null
    }
  }
  
  private flushMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected()) {
      const message = this.messageQueue.shift()!
      this.ws!.send(JSON.stringify(message))
    }
  }
}

export const websocketService = new WebSocketService()