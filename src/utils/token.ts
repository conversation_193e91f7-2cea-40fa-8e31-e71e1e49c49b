const TOKEN_KEY = 'biormika_access_token'
const REFRESH_TOKEN_KEY = 'biormika_refresh_token'

export const tokenStorage = {
  getAccessToken: (): string | null => {
    try {
      return localStorage.getItem(TOKEN_KEY)
    } catch (error) {
      console.error('Error getting access token:', error)
      return null
    }
  },
  
  setAccessToken: (token: string): void => {
    try {
      localStorage.setItem(TOKEN_KEY, token)
    } catch (error) {
      console.error('Error setting access token:', error)
    }
  },
  
  getRefreshToken: (): string | null => {
    try {
      return localStorage.getItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error('Error getting refresh token:', error)
      return null
    }
  },
  
  setRefreshToken: (token: string): void => {
    try {
      localStorage.setItem(REFRESH_TOKEN_KEY, token)
    } catch (error) {
      console.error('Error setting refresh token:', error)
    }
  },
  
  setTokens: (accessToken: string, refreshToken?: string): void => {
    tokenStorage.setAccessToken(accessToken)
    if (refreshToken) {
      tokenStorage.setRefreshToken(refreshToken)
    }
  },
  
  clearTokens: (): void => {
    try {
      localStorage.removeItem(TOKEN_KEY)
      localStorage.removeItem(REFRESH_TOKEN_KEY)
    } catch (error) {
      console.error('Error clearing tokens:', error)
    }
  },
  
  isTokenExpired: (token: string): boolean => {
    try {
      const payload = JSON.parse(atob(token.split('.')[1]))
      const exp = payload.exp * 1000
      return Date.now() >= exp
    } catch {
      return true
    }
  },
}