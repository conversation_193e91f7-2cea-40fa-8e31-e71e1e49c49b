export const isDevelopment = process.env.NODE_ENV === 'development'
export const isProduction = process.env.NODE_ENV === 'production'

export const logger = {
  log: (...args: unknown[]) => {
    if (isDevelopment) {
      console.log(...args)
    }
  },
  warn: (...args: unknown[]) => {
    if (isDevelopment) {
      console.warn(...args)
    }
  },
  error: (...args: unknown[]) => {
    console.error(...args)
  },
}

export const debugRedux = (state: unknown, action?: { type: string; payload?: unknown }) => {
  if (isDevelopment) {
    console.group('Redux Debug')
    console.log('State:', state)
    if (action) console.log('Action:', action)
    console.groupEnd()
  }
}