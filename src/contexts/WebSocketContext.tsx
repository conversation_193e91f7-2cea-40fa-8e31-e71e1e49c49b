import React, { createContext, useEffect, useCallback, useState } from 'react'
import { websocketService } from '../services/websocket.service'
import type { WebSocketStatus } from '../services/websocket.service'
import type { 
  AnalysisProgressMessage, 
  AnalysisCompleteMessage, 
  FileValidatedMessage 
} from '../services/websocket.service'
import { useAppSelector } from '../store'
import { WEBSOCKET_EVENTS } from '../constants/api'

interface WebSocketContextValue {
  status: WebSocketStatus
  isConnected: boolean
  connect: () => void
  disconnect: () => void
  send: (event: string, data: unknown) => void
  subscribeToAnalysisProgress: (handler: (data: AnalysisProgressMessage) => void) => () => void
  subscribeToAnalysisComplete: (handler: (data: AnalysisCompleteMessage) => void) => () => void
  subscribeToFileValidated: (handler: (data: FileValidatedMessage) => void) => () => void
}

const WebSocketContext = createContext<WebSocketContextValue | undefined>(undefined)

export { WebSocketContext }

interface WebSocketProviderProps {
  children: React.ReactNode
  autoConnect?: boolean
}

export function WebSocketProvider({ children, autoConnect = true }: WebSocketProviderProps) {
  const [status, setStatus] = useState<WebSocketStatus>('disconnected')
  const isAuthenticated = useAppSelector(state => state.auth.isAuthenticated)
  const token = useAppSelector(state => state.auth.token)
  
  useEffect(() => {
    const handleStatusChange = (newStatus: WebSocketStatus) => {
      setStatus(newStatus)
    }
    
    websocketService.onConnectionChange(handleStatusChange)
    
    return () => {
      websocketService.offConnectionChange(handleStatusChange)
    }
  }, [])
  
  useEffect(() => {
    if (autoConnect && isAuthenticated && token && import.meta.env.VITE_ENABLE_WEBSOCKET === 'true') {
      websocketService.connect()
    } else if (!isAuthenticated || !token) {
      websocketService.disconnect()
    }
    
    return () => {
      if (autoConnect) {
        websocketService.disconnect()
      }
    }
  }, [isAuthenticated, token, autoConnect])
  
  const connect = useCallback(() => {
    websocketService.connect()
  }, [])
  
  const disconnect = useCallback(() => {
    websocketService.disconnect()
  }, [])
  
  const send = useCallback((event: string, data: unknown) => {
    websocketService.send(event, data)
  }, [])
  
  const subscribeToAnalysisProgress = useCallback((handler: (data: AnalysisProgressMessage) => void) => {
    websocketService.on(WEBSOCKET_EVENTS.analysisProgress, handler)
    return () => websocketService.off(WEBSOCKET_EVENTS.analysisProgress, handler)
  }, [])
  
  const subscribeToAnalysisComplete = useCallback((handler: (data: AnalysisCompleteMessage) => void) => {
    websocketService.on(WEBSOCKET_EVENTS.analysisComplete, handler)
    return () => websocketService.off(WEBSOCKET_EVENTS.analysisComplete, handler)
  }, [])
  
  const subscribeToFileValidated = useCallback((handler: (data: FileValidatedMessage) => void) => {
    websocketService.on(WEBSOCKET_EVENTS.fileValidated, handler)
    return () => websocketService.off(WEBSOCKET_EVENTS.fileValidated, handler)
  }, [])
  
  const value: WebSocketContextValue = {
    status,
    isConnected: status === 'connected',
    connect,
    disconnect,
    send,
    subscribeToAnalysisProgress,
    subscribeToAnalysisComplete,
    subscribeToFileValidated
  }
  
  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  )
}